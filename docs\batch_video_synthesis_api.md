# 批量视频合成API文档

## 概述

批量视频合成功能允许用户一次性选择多个数字人形象和声音，系统会自动将其拆分为多个独立的视频合成任务进行处理。

## 数据库表结构更新

在使用批量功能前，需要先执行以下SQL语句更新数据库表结构：

```sql
-- 添加批次ID字段
ALTER TABLE `platform_video` ADD COLUMN `batch_id` bigint(20) NULL DEFAULT NULL COMMENT '批次ID' AFTER `version`;

-- 添加数字人名称字段  
ALTER TABLE `platform_video` ADD COLUMN `digital_human_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数字人名称' AFTER `batch_id`;

-- 为批次ID字段添加索引，提高查询性能
CREATE INDEX `idx_batch_id` ON `platform_video` (`batch_id`);

-- 为数字人名称字段添加索引
CREATE INDEX `idx_digital_human_name` ON `platform_video` (`digital_human_name`);
```

## API接口

### 1. 批量创建视频合成任务

**接口地址：** `POST /platform/video/batch/create`

**请求参数：**

```json
{
  "version": "H",
  "model": "default",
  "digitalHumans": [
    {
      "name": "张三",
      "avatarPath": "/path/to/avatar1.mp4",
      "voicePath": "/path/to/voice1.wav",
      "dialogueText": "你好，我是张三，很高兴见到大家。"
    },
    {
      "name": "李四",
      "avatarPath": "/path/to/avatar2.mp4",
      "voicePath": "/path/to/voice2.wav",
      "dialogueText": "大家好，我是李四，欢迎来到我们的直播间。"
    },
    {
      "name": "王五",
      "avatarPath": "/path/to/avatar3.mp4",
      "voicePath": "/path/to/voice3.wav",
      "dialogueText": "各位朋友们好，我是王五，今天为大家带来精彩内容。"
    }
  ]
}
```

**参数说明：**

- `version`: 视频合成版本（M/H/V）
- `model`: 模型代码
- `digitalHumans`: 数字人配置列表
  - `name`: 数字人名称（可选，不填则自动生成序号）
  - `avatarPath`: 形象文件路径
  - `voicePath`: 声音文件路径
  - `dialogueText`: 对话内容

**响应示例：**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "batchId": 1722150000000,
    "batchNumber": "VID_20250728_143000_ABC123",
    "totalTasks": 3,
    "taskIds": [1001, 1002, 1003],
    "message": "批量任务创建成功，共生成 3 个子任务"
  }
}
```

### 2. 查询批量任务状态

**接口地址：** `GET /platform/video/batch/status/{batchId}`

**路径参数：**

- `batchId`: 批次ID

**响应示例：**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "batchId": 1722150000000,
    "batchNumber": "VID_20250728_143000_ABC123",
    "batchStatus": "2",
    "totalTasks": 3,
    "completedTasks": 1,
    "failedTasks": 0,
    "processingTasks": 2,
    "pendingTasks": 0,
    "subTasks": [
      {
        "id": 1001,
        "digitalHumanName": "张三",
        "status": "3",
        "resultVideo": "/path/to/result1.mp4",
        "completeAt": "2025-07-28 14:35:00"
      },
      {
        "id": 1002,
        "digitalHumanName": "李四",
        "status": "2",
        "resultVideo": null,
        "completeAt": null
      },
      {
        "id": 1003,
        "digitalHumanName": "王五",
        "status": "2",
        "resultVideo": null,
        "completeAt": null
      }
    ]
  }
}
```

**状态说明：**

- `batchStatus`: 批次状态
  - `1`: 待处理
  - `2`: 处理中
  - `3`: 全部完成
  - `4`: 部分失败
  - `5`: 全部失败

### 3. 更新批量任务状态

**接口地址：** `POST /platform/video/batch/update/{batchId}`

**路径参数：**

- `batchId`: 批次ID

**响应示例：**

```json
{
  "code": 200,
  "msg": "批量任务状态更新成功"
}
```

## 使用说明

1. **批量创建**：用户可以一次性提交多个数字人配置，系统会自动拆分为独立的任务
2. **任务管理**：每个子任务都有独立的ID，可以单独查询和管理
3. **状态跟踪**：可以通过批次ID查询整个批次的进度和状态
4. **数据存储**：
   - 批次信息存储在 `operation`字段的JSON中
   - 对话内容临时存储在 `callback_url`字段中
   - 数字人名称存储在 `digital_human_name`字段中
   - 批次ID存储在 `batch_id`字段中

## 注意事项

1. 批量任务的处理顺序没有限制，可以并行处理
2. 每个子任务的处理逻辑与单个任务完全相同
3. 建议在创建批量任务后定期查询状态，监控处理进度
4. 数字人名称如果不指定，系统会自动生成序号（数字人1、数字人2...）
