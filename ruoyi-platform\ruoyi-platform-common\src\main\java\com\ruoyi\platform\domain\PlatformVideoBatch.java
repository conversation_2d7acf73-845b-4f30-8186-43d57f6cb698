package com.ruoyi.platform.domain;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 批量视频合成任务对象 platform_video_batch
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Schema(description = "批量视频合成任务对象")
public class PlatformVideoBatch extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 批次ID */
    @Schema(title = "批次ID")
    private Long batchId;

    /** 批次编号 */
    @Schema(title = "批次编号")
    @Excel(name = "批次编号")
    private String batchNumber;

    /** 批次名称 */
    @Schema(title = "批次名称")
    @Excel(name = "批次名称")
    private String batchName;

    /** 总任务数 */
    @Schema(title = "总任务数")
    @Excel(name = "总任务数")
    private Integer totalTasks;

    /** 已完成任务数 */
    @Schema(title = "已完成任务数")
    @Excel(name = "已完成任务数")
    private Integer completedTasks;

    /** 失败任务数 */
    @Schema(title = "失败任务数")
    @Excel(name = "失败任务数")
    private Integer failedTasks;

    /** 批次状态 1待处理 2处理中 3已完成 4部分失败 5全部失败 */
    @Schema(title = "批次状态")
    @Excel(name = "批次状态")
    private String batchStatus;

    /** 回调URL */
    @Schema(title = "回调URL")
    @Excel(name = "回调URL")
    private String callbackUrl;

    /** 批次开始时间 */
    @Schema(title = "批次开始时间")
    @Excel(name = "批次开始时间")
    private Date startTime;

    /** 批次完成时间 */
    @Schema(title = "批次完成时间")
    @Excel(name = "批次完成时间")
    private Date completeTime;

    /** 创建时间 */
    @Schema(title = "创建时间")
    @Excel(name = "创建时间")
    private Date createdAt;

    /** 修改时间 */
    @Schema(title = "修改时间")
    @Excel(name = "修改时间")
    private Date updatedAt;

    /** 批次描述 */
    @Schema(title = "批次描述")
    @Excel(name = "批次描述")
    private String description;

    /** 数字人配置信息(JSON格式存储) */
    @Schema(title = "数字人配置信息")
    @Excel(name = "数字人配置信息")
    private String digitalHumanConfig;

    /** 关联的子任务列表 */
    @TableField(exist = false)
    @Schema(title = "关联的子任务列表")
    private List<PlatformVideo> subTasks;

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public Integer getTotalTasks() {
        return totalTasks;
    }

    public void setTotalTasks(Integer totalTasks) {
        this.totalTasks = totalTasks;
    }

    public Integer getCompletedTasks() {
        return completedTasks;
    }

    public void setCompletedTasks(Integer completedTasks) {
        this.completedTasks = completedTasks;
    }

    public Integer getFailedTasks() {
        return failedTasks;
    }

    public void setFailedTasks(Integer failedTasks) {
        this.failedTasks = failedTasks;
    }

    public String getBatchStatus() {
        return batchStatus;
    }

    public void setBatchStatus(String batchStatus) {
        this.batchStatus = batchStatus;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDigitalHumanConfig() {
        return digitalHumanConfig;
    }

    public void setDigitalHumanConfig(String digitalHumanConfig) {
        this.digitalHumanConfig = digitalHumanConfig;
    }

    public List<PlatformVideo> getSubTasks() {
        return subTasks;
    }

    public void setSubTasks(List<PlatformVideo> subTasks) {
        this.subTasks = subTasks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("batchId", getBatchId())
                .append("batchNumber", getBatchNumber())
                .append("batchName", getBatchName())
                .append("totalTasks", getTotalTasks())
                .append("completedTasks", getCompletedTasks())
                .append("failedTasks", getFailedTasks())
                .append("batchStatus", getBatchStatus())
                .append("callbackUrl", getCallbackUrl())
                .append("startTime", getStartTime())
                .append("completeTime", getCompleteTime())
                .append("createdAt", getCreatedAt())
                .append("createBy", getCreateBy())
                .append("updatedAt", getUpdatedAt())
                .append("updateBy", getUpdateBy())
                .append("remark", getRemark())
                .append("description", getDescription())
                .append("digitalHumanConfig", getDigitalHumanConfig())
                .toString();
    }
}
