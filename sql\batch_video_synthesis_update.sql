-- 批量视频合成功能数据库表结构更新
-- 在现有的platform_video表中添加批次相关字段

-- 添加批次ID字段
ALTER TABLE `platform_video` ADD COLUMN `batch_id` bigint(20) NULL DEFAULT NULL COMMENT '批次ID' AFTER `version`;

-- 添加数字人名称字段  
ALTER TABLE `platform_video` ADD COLUMN `digital_human_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数字人名称' AFTER `batch_id`;

-- 为批次ID字段添加索引，提高查询性能
CREATE INDEX `idx_batch_id` ON `platform_video` (`batch_id`);

-- 为数字人名称字段添加索引
CREATE INDEX `idx_digital_human_name` ON `platform_video` (`digital_human_name`);

-- 查看表结构
-- DESC platform_video;
