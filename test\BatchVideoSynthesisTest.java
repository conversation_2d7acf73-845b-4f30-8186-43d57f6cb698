package com.ruoyi.platform.test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.ruoyi.platform.domain.dto.BatchVideoSynthesisRequest;
import com.ruoyi.platform.service.IPlatformVideoService;

/**
 * 批量视频合成功能测试
 */
@SpringBootTest
public class BatchVideoSynthesisTest {

    @Autowired
    private IPlatformVideoService platformVideoService;

    @Test
    public void testCreateBatchVideoSynthesis() {
        // 创建批量请求
        BatchVideoSynthesisRequest request = new BatchVideoSynthesisRequest();
        request.setVersion("H");
        request.setModel("default");

        // 创建数字人配置列表
        List<BatchVideoSynthesisRequest.DigitalHumanConfig> digitalHumans = new ArrayList<>();
        
        // 数字人1
        BatchVideoSynthesisRequest.DigitalHumanConfig human1 = new BatchVideoSynthesisRequest.DigitalHumanConfig();
        human1.setName("张三");
        human1.setAvatarPath("/path/to/avatar1.mp4");
        human1.setVoicePath("/path/to/voice1.wav");
        human1.setDialogueText("你好，我是张三，很高兴见到大家。");
        digitalHumans.add(human1);

        // 数字人2
        BatchVideoSynthesisRequest.DigitalHumanConfig human2 = new BatchVideoSynthesisRequest.DigitalHumanConfig();
        human2.setName("李四");
        human2.setAvatarPath("/path/to/avatar2.mp4");
        human2.setVoicePath("/path/to/voice2.wav");
        human2.setDialogueText("大家好，我是李四，欢迎来到我们的直播间。");
        digitalHumans.add(human2);

        // 数字人3
        BatchVideoSynthesisRequest.DigitalHumanConfig human3 = new BatchVideoSynthesisRequest.DigitalHumanConfig();
        human3.setName("王五");
        human3.setAvatarPath("/path/to/avatar3.mp4");
        human3.setVoicePath("/path/to/voice3.wav");
        human3.setDialogueText("各位朋友们好，我是王五，今天为大家带来精彩内容。");
        digitalHumans.add(human3);

        request.setDigitalHumans(digitalHumans);

        // 执行批量创建
        Map<String, Object> result = platformVideoService.createBatchVideoSynthesis(request);
        
        // 验证结果
        System.out.println("批量创建结果: " + result);
        
        if (Boolean.TRUE.equals(result.get("success"))) {
            Long batchId = (Long) result.get("batchId");
            System.out.println("批次ID: " + batchId);
            System.out.println("总任务数: " + result.get("totalTasks"));
            
            // 查询批量任务状态
            Map<String, Object> statusResult = platformVideoService.getBatchTaskStatus(batchId);
            System.out.println("批量任务状态: " + statusResult);
        }
    }

    @Test
    public void testGetBatchTaskStatus() {
        // 假设有一个已存在的批次ID
        Long batchId = 1722150000000L; // 示例批次ID
        
        Map<String, Object> result = platformVideoService.getBatchTaskStatus(batchId);
        System.out.println("批量任务状态查询结果: " + result);
    }
}
