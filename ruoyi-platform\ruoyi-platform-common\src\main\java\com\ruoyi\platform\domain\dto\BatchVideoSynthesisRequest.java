package com.ruoyi.platform.domain.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 批量视频合成请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Schema(description = "批量视频合成请求DTO")
public class BatchVideoSynthesisRequest {

    /** 数字人配置列表 */
    @Schema(title = "数字人配置列表", description = "数字人形象与声音的配置组合", required = true)
    private List<DigitalHumanConfig> digitalHumans;

    /** 合成版本 */
    @Schema(title = "合成版本", description = "视频合成版本(M/H/V)", example = "H")
    private String version;

    /** 模型代码 */
    @Schema(title = "模型代码", description = "使用的合成模型代码")
    private String model;

    /**
     * 数字人配置内部类
     */
    @Schema(description = "数字人配置")
    public static class DigitalHumanConfig {
        
        /** 数字人名称/序号 */
        @Schema(title = "数字人名称", description = "数字人的名称或序号")
        private String name;

        /** 形象文件路径 */
        @Schema(title = "形象文件路径", description = "数字人形象视频文件路径")
        private String avatarPath;

        /** 声音文件路径 */
        @Schema(title = "声音文件路径", description = "数字人声音音频文件路径")
        private String voicePath;

        /** 对话内容 */
        @Schema(title = "对话内容", description = "该数字人的对话文本内容")
        private String dialogueText;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAvatarPath() {
            return avatarPath;
        }

        public void setAvatarPath(String avatarPath) {
            this.avatarPath = avatarPath;
        }

        public String getVoicePath() {
            return voicePath;
        }

        public void setVoicePath(String voicePath) {
            this.voicePath = voicePath;
        }

        public String getDialogueText() {
            return dialogueText;
        }

        public void setDialogueText(String dialogueText) {
            this.dialogueText = dialogueText;
        }
    }

    public List<DigitalHumanConfig> getDigitalHumans() {
        return digitalHumans;
    }

    public void setDigitalHumans(List<DigitalHumanConfig> digitalHumans) {
        this.digitalHumans = digitalHumans;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}
