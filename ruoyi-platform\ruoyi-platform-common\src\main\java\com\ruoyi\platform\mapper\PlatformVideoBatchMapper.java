package com.ruoyi.platform.mapper;

import java.util.List;

import com.ruoyi.platform.domain.PlatformVideoBatch;

/**
 * 批量视频合成任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface PlatformVideoBatchMapper {
    
    /**
     * 查询批量视频合成任务
     * 
     * @param batchId 批量视频合成任务主键
     * @return 批量视频合成任务
     */
    public PlatformVideoBatch selectPlatformVideoBatchByBatchId(Long batchId);

    /**
     * 查询批量视频合成任务列表
     * 
     * @param platformVideoBatch 批量视频合成任务
     * @return 批量视频合成任务集合
     */
    public List<PlatformVideoBatch> selectPlatformVideoBatchList(PlatformVideoBatch platformVideoBatch);

    /**
     * 新增批量视频合成任务
     * 
     * @param platformVideoBatch 批量视频合成任务
     * @return 结果
     */
    public int insertPlatformVideoBatch(PlatformVideoBatch platformVideoBatch);

    /**
     * 修改批量视频合成任务
     * 
     * @param platformVideoBatch 批量视频合成任务
     * @return 结果
     */
    public int updatePlatformVideoBatch(PlatformVideoBatch platformVideoBatch);

    /**
     * 删除批量视频合成任务
     * 
     * @param batchId 批量视频合成任务主键
     * @return 结果
     */
    public int deletePlatformVideoBatchByBatchId(Long batchId);

    /**
     * 批量删除批量视频合成任务
     * 
     * @param batchIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatformVideoBatchByBatchIds(Long[] batchIds);

    /**
     * 根据批次编号查询批量任务
     * 
     * @param batchNumber 批次编号
     * @return 批量视频合成任务
     */
    public PlatformVideoBatch selectPlatformVideoBatchByBatchNumber(String batchNumber);

    /**
     * 查询用户的批量任务列表
     * 
     * @param createBy 创建人
     * @return 批量视频合成任务集合
     */
    public List<PlatformVideoBatch> selectPlatformVideoBatchByCreateBy(String createBy);

    /**
     * 更新批次状态
     * 
     * @param batchId 批次ID
     * @param batchStatus 批次状态
     * @param completedTasks 已完成任务数
     * @param failedTasks 失败任务数
     * @return 结果
     */
    public int updateBatchStatus(Long batchId, String batchStatus, Integer completedTasks, Integer failedTasks);
}
