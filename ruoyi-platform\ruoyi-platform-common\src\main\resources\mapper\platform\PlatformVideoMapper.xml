<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformVideoMapper">

    <resultMap type="PlatformVideo" id="PlatformVideoResult">
        <result property="id" column="id" />
        <result property="number" column="number" />
        <result property="drivenAudio" column="driven_audio" />
        <result property="drivenVideo" column="driven_video" />
        <result property="resultVideo" column="result_video" />
        <result property="completeAt" column="complete_at" />
        <result property="status" column="status" />
        <result property="callbackUrl" column="callback_url" />
        <result property="model" column="model" />
        <result property="taskNo" column="task_no" />
        <result property="createdAt" column="created_at" />
        <result property="createBy" column="create_by" />
        <result property="updatedAt" column="updated_at" />
        <result property="updateBy" column="update_by" />
        <result property="remark" column="remark" />
        <result property="operation" column="operation" />
        <result property="drivenVideoMd5" column="video_md5" />
        <result property="version" column="version" />
        <result property="batchId" column="batch_id" />
        <result property="digitalHumanName" column="digital_human_name" />
    </resultMap>

    <sql id="selectPlatformVideoVo">
        select v.id, v.number, v.driven_audio, v.driven_video, v.result_video, v.complete_at, v.status,
        v.callback_url, v.model, v.task_no, v.created_at, v.create_by, v.updated_at, v.update_by,v.remark,v.version,
        v.operation from platform_video v left join sys_user u on u.user_name = v.create_by
        left join sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPlatformVideoList" parameterType="PlatformVideo" resultMap="PlatformVideoResult">
        <include refid="selectPlatformVideoVo"/>
        <where>
            <if test="number != null and number != ''"> and v.number = #{number}</if>
            <if test="status != null and status !=''"> and v.status = #{status}</if>
            <if test="batchId != null"> and v.batch_id = #{batchId}</if>
            <if test="digitalHumanName != null and digitalHumanName != ''"> and v.digital_human_name = #{digitalHumanName}</if>
            ${params.dataScope}
        </where>
        order by v.created_at desc
    </select>

    <select id="selectPlatformVideoById" parameterType="Long" resultMap="PlatformVideoResult">
        <include refid="selectPlatformVideoVo"/>
        <where>
            v.id = #{id}
        </where>
    </select>

    <insert id="insertPlatformVideo" parameterType="PlatformVideo" useGeneratedKeys="true" keyProperty="id">
        insert into platform_video
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="number != null and number != ''">number,</if>
            <if test="drivenAudio != null and drivenAudio != ''">driven_audio,</if>
            <if test="drivenVideo != null and drivenVideo != ''">driven_video,</if>
            <if test="resultVideo != null">result_video,</if>
            <if test="completeAt != null">complete_at,</if>
            <if test="status != null">status,</if>
            <if test="callbackUrl != null">callback_url,</if>
            <if test="model != null">model,</if>
            <if test="taskNo != null">task_no,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="operation != null">operation,</if>
            <if test="version != null and version != ''">version,</if>
            <if test="batchId != null">batch_id,</if>
            <if test="digitalHumanName != null and digitalHumanName != ''">digital_human_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="number != null and number != ''">#{number},</if>
            <if test="drivenAudio != null and drivenAudio != ''">#{drivenAudio},</if>
            <if test="drivenVideo != null and drivenVideo != ''">#{drivenVideo},</if>
            <if test="resultVideo != null">#{resultVideo},</if>
            <if test="completeAt != null">#{completeAt},</if>
            <if test="status != null">#{status},</if>
            <if test="callbackUrl != null">#{callbackUrl},</if>
            <if test="model != null">#{model},</if>
            <if test="taskNo != null">#{taskNo},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="operation != null">#{operation},</if>
            <if test="version != null and version != ''">#{version},</if>
            <if test="batchId != null">#{batchId},</if>
            <if test="digitalHumanName != null and digitalHumanName != ''">#{digitalHumanName},</if>
        </trim>
    </insert>

    <update id="updatePlatformVideo" parameterType="PlatformVideo">
        update platform_video
        <trim prefix="SET" suffixOverrides=",">
            <if test="number != null and number != ''">number = #{number},</if>
            <if test="drivenAudio != null and drivenAudio != ''">driven_audio = #{drivenAudio},</if>
            <if test="drivenVideo != null and drivenVideo != ''">driven_video = #{drivenVideo},</if>
            <if test="resultVideo != null">result_video = #{resultVideo},</if>
            <if test="completeAt != null">complete_at = #{completeAt},</if>
            <if test="status != null">status = #{status},</if>
            <if test="callbackUrl != null">callback_url = #{callbackUrl},</if>
            <if test="model != null">model = #{model},</if>
            <if test="taskNo != null">task_no = #{taskNo},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="version != null and version != ''">version = #{version},</if>
        </trim>
        where platform_video.id = #{id}
    </update>

    <delete id="deletePlatformVideoById" parameterType="Long">
        delete from platform_video where id = #{id}
    </delete>

    <delete id="deletePlatformVideoByIds" parameterType="String">
        delete from platform_video where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <sql id="selectPlatformVideoVoMd5">
        select v.id, v.number, v.driven_audio, v.driven_video, v.result_video, v.complete_at, v.status, 
        v.callback_url, v.model, v.task_no, v.created_at, v.create_by, v.updated_at, v.update_by,v.remark,v.version,
        v.operation,i.video_md5 from platform_video v left join sys_user u on u.user_name = v.create_by 
        left join sys_dept d on u.dept_id = d.dept_id left join platform_image i on i.image_address = v.driven_video
    </sql>


    <select id="selectPlatformVideoOne" parameterType="PlatformVideo" resultMap="PlatformVideoResult">
        <include refid="selectPlatformVideoVoMd5"/>
        <where>
            <if test="number != null and number != ''"> AND v.number = #{number} </if>
            <if test="status != null and status != 0"> AND v.status = #{status} </if>
            <if test="version != null and version != ''"> AND v.version = #{version} </if>
        </where>
        order by v.id asc limit 1
    </select>
</mapper>