package com.ruoyi.platform.domain;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 视频合成对象 platform_video
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@Schema(description = "视频合成对象")
public class PlatformVideo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    @Schema(title = "任务ID")
    private Long id;

    /** 视频名称 */
    @Schema(title = "视频名称")
    @Excel(name = "视频名称")
    private String number;

    /** 驱动音频 */
    @Schema(title = "驱动音频")
    @Excel(name = "驱动音频")
    private String drivenAudio;

    /** 驱动视频 */
    @Schema(title = "驱动视频")
    @Excel(name = "驱动视频")
    private String drivenVideo;

    /** 结果视频 */
    @Schema(title = "结果视频")
    @Excel(name = "结果视频")
    private String resultVideo;

    /** 合成完成时间 */
    @Schema(title = "合成完成时间")
    @Excel(name = "合成完成时间")
    private Date completeAt;

    /** 1待处理 2待处理 3成功 4失败 */
    @Schema(title = "1待处理 2待处理 3成功 4失败")
    @Excel(name = "1待处理 2待处理 3成功 4失败")
    private String status;

    /** 回调URL */
    @Schema(title = "回调URL")
    @Excel(name = "回调URL")
    private String callbackUrl;

    /** 模型名称 */
    @Schema(title = "模型名称")
    @Excel(name = "模型名称")
    private String model;

    /** 服务端任务编号 */
    @Schema(title = "服务端任务编号")
    @Excel(name = "服务端任务编号")
    private String taskNo;

    /** 创建时间 */
    @Schema(title = "创建时间")
    @Excel(name = "创建时间")
    private Date createdAt;

    /** 修改时间 */
    @Schema(title = "修改时间")
    @Excel(name = "修改时间")
    private Date updatedAt;

    /** json信息 */
    @Schema(title = "json信息")
    @Excel(name = "json信息")
    private String operation;

    /** 驱动视频MD5 */
    @Schema(title = "驱动视频MD5")
    @Excel(name = "驱动视频MD5")
    private String drivenVideoMd5;

    @TableField(exist = false)
    @Schema(title = "驱动音频MD5")
    private String drivenAudioMd5;

    @TableField(exist = false)
    @Schema(title = "调整值")
    private String bboxShiftValue;

    @TableField(exist = false)
    @Schema(title = "优先级")
    private String videoPriority;

    @TableField(exist = false)
    @Schema(title = "状态码")
    private String code;

    @TableField(exist = false)
    @Schema(title = "状态消息")
    private String videoMessage;

    /** 合成版本 */
    @Schema(title = "合成版本")
    @Excel(name = "合成版本")
    private String version;

    /** 批次ID */
    @Schema(title = "批次ID")
    @Excel(name = "批次ID")
    private Long batchId;

    /** 数字人名称 */
    @Schema(title = "数字人名称")
    @Excel(name = "数字人名称")
    private String digitalHumanName;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public String getDigitalHumanName() {
        return digitalHumanName;
    }

    public void setDigitalHumanName(String digitalHumanName) {
        this.digitalHumanName = digitalHumanName;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getNumber() {
        return number;
    }

    public void setDrivenAudio(String drivenAudio) {
        this.drivenAudio = drivenAudio;
    }

    public String getDrivenAudio() {
        return drivenAudio;
    }

    public void setDrivenVideo(String drivenVideo) {
        this.drivenVideo = drivenVideo;
    }

    public String getDrivenVideo() {
        return drivenVideo;
    }

    public void setResultVideo(String resultVideo) {
        this.resultVideo = resultVideo;
    }

    public String getResultVideo() {
        return resultVideo;
    }

    public void setCompleteAt(Date completeAt) {
        this.completeAt = completeAt;
    }

    public Date getCompleteAt() {
        return completeAt;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getModel() {
        return model;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getOperation() {
        return operation;
    }

    public JSONObject getOperationJson() {
        if (StringUtils.isEmpty(operation)) {
            return new JSONObject();
        }
        try {
            return JSONObject.parseObject(operation);
        } catch (Exception e) {
            return new JSONObject();
        }
    }

    public void setOperationJson(JSONObject json) {
        this.operation = json != null ? json.toString() : null;
    }

    public String getDrivenAudioMd5() {
        return getOperationJson().getString("driven_audio_md5");
    }

    public void setDrivenAudioMd5(String drivenAudioMd5) {
        JSONObject json = getOperationJson();
        json.put("driven_audio_md5", drivenAudioMd5);
        setOperationJson(json);
    }

    // public String getDrivenVideoMd5() {
    //     return getOperationJson().getString("driven_video_md5");
    // }

    // public void setDrivenVideoMd5(String drivenVideoMd5) {
    //     JSONObject json = getOperationJson();
    //     json.put("driven_video_md5", drivenVideoMd5);
    //     setOperationJson(json);
    // }

    public String getDrivenVideoMd5() {
        return drivenVideoMd5;
    }

    public void setDrivenVideoMd5(String drivenVideoMd5) {
        this.drivenVideoMd5 = drivenVideoMd5;
    }

    public String getBboxShiftValue() {
        return getOperationJson().getString("bbox_shift_value");
    }

    public void setBboxShiftValue(String bboxShiftValue) {
        JSONObject json = getOperationJson();
        json.put("bbox_shift_value", bboxShiftValue);
        setOperationJson(json);
    }

    public String getVideoPriority() {
        return getOperationJson().getString("video_priority");
    }

    public void setVideoPriority(String videoPriority) {
        JSONObject json = getOperationJson();
        json.put("video_priority", videoPriority);
        setOperationJson(json);
    }

    public String getCode() {
        return getOperationJson().getString("code");
    }

    public void setCode(String code) {
        JSONObject json = getOperationJson();
        json.put("code", code);
        setOperationJson(json);
    }

    public String getVideoMessage() {
        return getOperationJson().getString("video_message");
    }

    public void setVideoMessage(String videoMessage) {
        JSONObject json = getOperationJson();
        json.put("video_message", videoMessage);
        setOperationJson(json);
    }

    public String getModelPrice() {
        // 直接从operation JSON中获取model_price字段
        JSONObject json = getOperationJson();
        return json.getString("model_price");
    }

    public void setModelPrice(String modelPrice) {
        // 确保直接存入operation JSON的model_price字段
        JSONObject json = getOperationJson();
        json.put("model_price", modelPrice);
        setOperationJson(json);

        // 打印出来做调试
        System.out.println("已设置模型价位: " + modelPrice + ", JSON: " + json.toString());
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("number", getNumber())
                .append("drivenAudio", getDrivenAudio())
                .append("drivenVideo", getDrivenVideo())
                .append("resultVideo", getResultVideo())
                .append("completeAt", getCompleteAt())
                .append("status", getStatus())
                .append("callbackUrl", getCallbackUrl())
                .append("model", getModel())
                .append("taskNo", getTaskNo())
                .append("createdAt", getCreatedAt())
                .append("createBy", getCreateBy())
                .append("updatedAt", getUpdatedAt())
                .append("updateBy", getUpdateBy())
                .append("remark", getRemark())
                .append("operation", getOperation())
                .append("version", getVersion())
                .append("batchId", getBatchId())
                .append("digitalHumanName", getDigitalHumanName())
                .toString();
    }
}
